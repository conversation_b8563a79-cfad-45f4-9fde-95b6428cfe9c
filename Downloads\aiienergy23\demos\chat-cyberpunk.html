<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyberpunk Chat Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #0a0a0a;
            background-image: 
                linear-gradient(45deg, transparent 24%, rgba(0, 255, 255, 0.05) 25%, rgba(0, 255, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 255, 255, 0.05) 75%, rgba(0, 255, 255, 0.05) 76%, transparent 77%),
                linear-gradient(-45deg, transparent 24%, rgba(255, 0, 255, 0.05) 25%, rgba(255, 0, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(255, 0, 255, 0.05) 75%, rgba(255, 0, 255, 0.05) 76%, transparent 77%);
            background-size: 30px 30px;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            animation: backgroundPulse 4s ease-in-out infinite alternate;
        }

        @keyframes backgroundPulse {
            0% { filter: hue-rotate(0deg); }
            100% { filter: hue-rotate(10deg); }
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ffff;
            border-radius: 0;
            box-shadow: 
                0 0 20px #00ffff,
                inset 0 0 20px rgba(0, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            animation: containerGlow 2s ease-in-out infinite alternate;
        }

        @keyframes containerGlow {
            0% { box-shadow: 0 0 20px #00ffff, inset 0 0 20px rgba(0, 255, 255, 0.1); }
            100% { box-shadow: 0 0 30px #00ffff, inset 0 0 30px rgba(0, 255, 255, 0.2); }
        }

        .chat-header {
            background: linear-gradient(90deg, #000, #001a1a, #000);
            padding: 15px 20px;
            border-bottom: 1px solid #00ffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            color: #00ffff;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 0 10px #00ffff;
            animation: textGlow 1.5s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            0% { text-shadow: 0 0 10px #00ffff; }
            100% { text-shadow: 0 0 20px #00ffff, 0 0 30px #00ffff; }
        }

        .close-btn {
            background: transparent;
            border: 1px solid #ff0080;
            color: #ff0080;
            padding: 8px 12px;
            cursor: pointer;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #ff0080;
            color: #000;
            box-shadow: 0 0 15px #ff0080;
        }

        .welcome-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
            background: radial-gradient(circle at center, rgba(0, 255, 255, 0.05) 0%, transparent 70%);
        }

        .welcome-title {
            font-size: 3rem;
            font-weight: bold;
            color: #00ffff;
            margin-bottom: 20px;
            text-shadow: 0 0 20px #00ffff;
            animation: titlePulse 2s ease-in-out infinite;
        }

        @keyframes titlePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            color: #ff0080;
            margin-bottom: 40px;
            max-width: 500px;
            line-height: 1.6;
            text-shadow: 0 0 10px #ff0080;
        }

        .suggestions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            width: 100%;
            max-width: 600px;
        }

        .suggestion-card {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ffff;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: right;
            position: relative;
            overflow: hidden;
        }

        .suggestion-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .suggestion-card:hover {
            border-color: #ff0080;
            box-shadow: 0 0 20px rgba(255, 0, 128, 0.5);
            transform: translateY(-5px);
        }

        .suggestion-card:hover::before {
            left: 100%;
        }

        .suggestion-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #00ffff;
            margin-bottom: 10px;
            text-shadow: 0 0 5px #00ffff;
        }

        .suggestion-desc {
            font-size: 0.9rem;
            color: #ff0080;
            line-height: 1.4;
        }

        .input-section {
            padding: 20px;
            background: linear-gradient(90deg, #000, #001a1a, #000);
            border-top: 1px solid #00ffff;
        }

        .input-container {
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid #00ffff;
            padding: 15px;
            display: flex;
            align-items: flex-end;
            gap: 15px;
            transition: all 0.3s ease;
        }

        .input-container:focus-within {
            border-color: #ff0080;
            box-shadow: 0 0 15px rgba(255, 0, 128, 0.3);
        }

        .message-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #00ffff;
            font-size: 16px;
            font-family: inherit;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 120px;
        }

        .message-input::placeholder {
            color: #666;
        }

        .send-btn {
            background: linear-gradient(45deg, #00ffff, #ff0080);
            border: none;
            color: #000;
            width: 45px;
            height: 45px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .send-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.3s ease;
        }

        .send-btn:hover::before {
            left: 100%;
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 0 20px #00ffff;
        }

        .input-tools {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            justify-content: center;
        }

        .tool-btn {
            background: transparent;
            border: 1px solid #00ffff;
            color: #00ffff;
            padding: 8px 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            text-transform: uppercase;
        }

        .tool-btn:hover {
            background: #00ffff;
            color: #000;
            box-shadow: 0 0 10px #00ffff;
        }

        .messages-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: none;
            background: rgba(0, 0, 0, 0.5);
        }

        .message {
            margin-bottom: 20px;
            animation: messageSlide 0.5s ease;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .message.user {
            text-align: left;
        }

        .message.bot {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 75%;
            padding: 15px 20px;
            position: relative;
            word-wrap: break-word;
            font-size: 14px;
            line-height: 1.5;
            border: 1px solid;
        }

        .message.user .message-bubble {
            background: rgba(0, 255, 255, 0.1);
            color: #00ffff;
            border-color: #00ffff;
            text-shadow: 0 0 5px #00ffff;
        }

        .message.bot .message-bubble {
            background: rgba(255, 0, 128, 0.1);
            color: #ff0080;
            border-color: #ff0080;
            text-shadow: 0 0 5px #ff0080;
        }

        .typing-indicator {
            display: none;
            text-align: right;
            margin: 20px;
        }

        .typing-bubble {
            display: inline-block;
            background: rgba(255, 0, 128, 0.1);
            border: 1px solid #ff0080;
            padding: 15px 20px;
            color: #ff0080;
        }

        .typing-dots {
            display: inline-flex;
            gap: 5px;
            margin-left: 10px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #ff0080;
            animation: typingCyber 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingCyber {
            0%, 60%, 100% { 
                transform: scale(1);
                box-shadow: 0 0 5px #ff0080;
            }
            30% { 
                transform: scale(1.5);
                box-shadow: 0 0 15px #ff0080;
            }
        }

        .demo-label {
            position: absolute;
            top: 10px;
            left: 20px;
            background: rgba(0, 255, 255, 0.2);
            color: #00ffff;
            padding: 5px 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
            border: 1px solid #00ffff;
            text-transform: uppercase;
        }

        /* Scrollbar */
        .messages-area::-webkit-scrollbar {
            width: 10px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: #000;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: #00ffff;
            box-shadow: 0 0 10px #00ffff;
        }

        @media (max-width: 768px) {
            .suggestions-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-title {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="demo-label">CYBERPUNK MODE</div>
        
        <div class="chat-header">
            <div class="header-title">ENERGY.AI TERMINAL</div>
            <button class="close-btn" onclick="window.close()">EXIT</button>
        </div>

        <div class="welcome-section" id="welcomeSection">
            <h1 class="welcome-title">أهلاً وسهلاً</h1>
            <p class="welcome-subtitle">نظام الطاقة الذكي متصل - كيف يمكنني مساعدتك؟</p>
            
            <div class="suggestions-grid">
                <div class="suggestion-card" data-suggestion="تحليل استهلاك الطاقة">
                    <div class="suggestion-title">تحليل استهلاك الطاقة</div>
                    <div class="suggestion-desc">فحص شامل لأنظمة الطاقة وتحديد نقاط الضعف</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="نصائح توفير الطاقة">
                    <div class="suggestion-title">نصائح توفير الطاقة</div>
                    <div class="suggestion-desc">استراتيجيات متقدمة لتحسين الكفاءة</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الطاقة المتجددة">
                    <div class="suggestion-title">الطاقة المتجددة</div>
                    <div class="suggestion-desc">تقنيات المستقبل للطاقة المستدامة</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الأجهزة الذكية">
                    <div class="suggestion-title">الأجهزة الذكية</div>
                    <div class="suggestion-desc">شبكات الطاقة الذكية والأتمتة</div>
                </div>
            </div>
        </div>

        <div class="messages-area" id="messagesArea"></div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-bubble">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                معالجة البيانات...
            </div>
        </div>

        <div class="input-section">
            <div class="input-container">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="أدخل استفسارك..."
                    rows="1"
                ></textarea>
                <button class="send-btn" id="sendBtn" disabled>►</button>
            </div>
            
            <div class="input-tools">
                <button class="tool-btn">VOICE</button>
                <button class="tool-btn">SCAN</button>
                <button class="tool-btn">ANALYZE</button>
                <button class="tool-btn">RESEARCH</button>
            </div>
        </div>
    </div>

    <script>
        const welcomeSection = document.getElementById('welcomeSection');
        const messagesArea = document.getElementById('messagesArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        const suggestionCards = document.querySelectorAll('.suggestion-card');

        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            sendBtn.disabled = this.value.trim() === '';
        });

        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        suggestionCards.forEach(card => {
            card.addEventListener('click', function() {
                const suggestion = this.getAttribute('data-suggestion');
                startChat(suggestion);
            });
        });

        function startChat(message) {
            welcomeSection.style.display = 'none';
            messagesArea.style.display = 'block';
            addMessage(message, 'user');
            showTyping();
            
            setTimeout(() => {
                hideTyping();
                const responses = {
                    'تحليل استهلاك الطاقة': '[SYSTEM] تحليل مكتمل ✓\n\n🔍 الفحص الشامل:\n• استهلاك الذروة: تحديد الأوقات الحرجة\n• كفاءة الأجهزة: تقييم الأداء\n• نقاط الهدر: رصد التسريبات\n• التوصيات: خطة التحسين\n\n[STATUS] جاهز للتنفيذ',
                    'نصائح توفير الطاقة': '[ENERGY OPTIMIZATION PROTOCOL]\n\n⚡ استراتيجيات متقدمة:\n• LED UPGRADE: توفير 80%\n• SMART THERMOSTAT: تحكم ذكي\n• POWER MANAGEMENT: إدارة الأحمال\n• RENEWABLE INTEGRATION: دمج المتجددة\n\n[EFFICIENCY] +45% متوقع',
                    'الطاقة المتجددة': '[RENEWABLE SYSTEMS ONLINE]\n\n🌞 تقنيات المستقبل:\n• SOLAR ARRAYS: ألواح شمسية متطورة\n• WIND TURBINES: توربينات الرياح\n• ENERGY STORAGE: تخزين متقدم\n• GRID INTEGRATION: ربط الشبكات\n\n[ROI] 5-7 سنوات',
                    'الأجهزة الذكية': '[SMART GRID ACTIVATED]\n\n🤖 شبكة ذكية:\n• IoT SENSORS: أجهزة استشعار\n• AI CONTROL: تحكم ذكي\n• REAL-TIME MONITORING: مراقبة فورية\n• PREDICTIVE MAINTENANCE: صيانة تنبؤية\n\n[AUTOMATION] مفعل'
                };
                
                const response = responses[message] || '[SYSTEM] استفسار مستلم. معالجة البيانات...';
                addMessage(response, 'bot');
            }, 2500);
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            if (welcomeSection.style.display !== 'none') {
                startChat(message);
            } else {
                addMessage(message, 'user');
                showTyping();
                
                setTimeout(() => {
                    hideTyping();
                    addMessage('[PROCESSING] تم استلام طلبك. جاري التحليل والمعالجة...', 'bot');
                }, 1800);
            }

            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendBtn.disabled = true;
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.innerHTML = `<div class="message-bubble">${text.replace(/\n/g, '<br>')}</div>`;
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }
    </script>
</body>
</html>
