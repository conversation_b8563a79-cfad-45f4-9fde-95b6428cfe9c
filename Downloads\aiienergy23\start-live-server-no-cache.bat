@echo off
echo ========================================
echo   Energy.AI Live Server (No Cache)
echo ========================================
echo.
echo Starting Live Server with cache disabled...
echo URL: http://127.0.0.1:3000
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Kill any existing servers on port 3000
netstat -ano | findstr :3000 > nul
if %errorlevel% equ 0 (
    echo Stopping existing server on port 3000...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do taskkill /f /pid %%a > nul 2>&1
    timeout /t 2 > nul
)

REM Start Python server with specific settings
echo Starting Python HTTP Server...
python -m http.server 3000 --bind 127.0.0.1

REM If Python fails, try alternatives
if %errorlevel% neq 0 (
    echo Python failed, trying PHP...
    php -S 127.0.0.1:3000
    
    if %errorlevel% neq 0 (
        echo PHP failed, trying Node.js serve...
        npx serve -p 3000 -s . --cors --no-clipboard
        
        if %errorlevel% neq 0 (
            echo All servers failed!
            echo Please install Python, PHP, or Node.js
            pause
        )
    )
)

pause
