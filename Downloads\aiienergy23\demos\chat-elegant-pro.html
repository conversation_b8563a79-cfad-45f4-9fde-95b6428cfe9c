<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elegant Professional Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .chat-container {
            width: 90%;
            max-width: 850px;
            height: 90vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .chat-header {
            padding: 24px 28px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.8);
        }

        .header-brand {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .brand-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .brand-text {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
        }

        .close-btn {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            color: #718096;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: #edf2f7;
            color: #4a5568;
            transform: scale(1.05);
        }

        .welcome-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 48px 32px;
            text-align: center;
        }

        .welcome-title {
            font-size: 2.75rem;
            font-weight: 300;
            color: #2d3748;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .welcome-subtitle {
            font-size: 1.125rem;
            color: #718096;
            margin-bottom: 48px;
            max-width: 480px;
            line-height: 1.6;
            font-weight: 400;
        }

        .suggestions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            width: 100%;
            max-width: 580px;
        }

        .suggestion-card {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 16px;
            padding: 28px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: right;
            position: relative;
            overflow: hidden;
        }

        .suggestion-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .suggestion-card:hover {
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-4px);
        }

        .suggestion-card:hover::before {
            opacity: 1;
        }

        .suggestion-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            margin-right: auto;
            font-size: 20px;
            position: relative;
            z-index: 1;
        }

        .suggestion-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .suggestion-desc {
            font-size: 0.875rem;
            color: #718096;
            line-height: 1.5;
            position: relative;
            z-index: 1;
        }

        .input-section {
            padding: 28px;
            background: rgba(255, 255, 255, 0.9);
            border-top: 1px solid rgba(0, 0, 0, 0.08);
        }

        .input-container {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 16px 20px;
            display: flex;
            align-items: flex-end;
            gap: 16px;
            transition: all 0.3s ease;
        }

        .input-container:focus-within {
            border-color: #667eea;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .message-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #2d3748;
            font-size: 16px;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 120px;
            font-family: inherit;
            line-height: 1.5;
        }

        .message-input::placeholder {
            color: #a0aec0;
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .send-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
        }

        .input-tools {
            display: flex;
            gap: 16px;
            margin-top: 20px;
            justify-content: center;
        }

        .tool-btn {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.08);
            color: #718096;
            padding: 10px 18px;
            border-radius: 24px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .tool-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.2);
            color: #667eea;
            transform: translateY(-1px);
        }

        .messages-area {
            flex: 1;
            padding: 28px;
            overflow-y: auto;
            display: none;
        }

        .message {
            margin-bottom: 28px;
            animation: messageSlide 0.4s ease;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            text-align: left;
        }

        .message.bot {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 75%;
            padding: 18px 24px;
            border-radius: 20px;
            position: relative;
            word-wrap: break-word;
            font-size: 15px;
            line-height: 1.6;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-bottom-left-radius: 8px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .message.bot .message-bubble {
            background: rgba(255, 255, 255, 0.9);
            color: #2d3748;
            border: 1px solid rgba(0, 0, 0, 0.08);
            border-bottom-right-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .typing-indicator {
            display: none;
            text-align: right;
            margin: 28px;
        }

        .typing-bubble {
            display: inline-block;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.08);
            padding: 18px 24px;
            border-radius: 20px;
            border-bottom-right-radius: 8px;
            color: #718096;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .typing-dots {
            display: inline-flex;
            gap: 4px;
            margin-left: 10px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: typingElegant 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingElegant {
            0%, 60%, 100% { 
                transform: translateY(0);
                opacity: 0.4;
            }
            30% { 
                transform: translateY(-12px);
                opacity: 1;
            }
        }

        .demo-label {
            position: absolute;
            top: 20px;
            left: 28px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* Scrollbar */
        .messages-area::-webkit-scrollbar {
            width: 8px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 4px;
        }

        .messages-area::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.5);
        }

        @media (max-width: 768px) {
            .suggestions-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-title {
                font-size: 2.25rem;
            }
            
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="demo-label">Elegant Professional</div>
        
        <div class="chat-header">
            <div class="header-brand">
                <div class="brand-icon">E</div>
                <div class="brand-text">Energy Assistant</div>
            </div>
            <button class="close-btn" onclick="window.close()">×</button>
        </div>

        <div class="welcome-section" id="welcomeSection">
            <h1 class="welcome-title">أهلاً وسهلاً</h1>
            <p class="welcome-subtitle">مساعدك الذكي في عالم الطاقة المستدامة والحلول الموفرة</p>
            
            <div class="suggestions-grid">
                <div class="suggestion-card" data-suggestion="تحليل استهلاك الطاقة">
                    <div class="suggestion-icon">📊</div>
                    <div class="suggestion-title">تحليل استهلاك الطاقة</div>
                    <div class="suggestion-desc">تحليل شامل ومفصل لأنماط استهلاك الطاقة في منشأتك</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="نصائح توفير الطاقة">
                    <div class="suggestion-icon">💡</div>
                    <div class="suggestion-title">نصائح توفير الطاقة</div>
                    <div class="suggestion-desc">استراتيجيات مبتكرة وعملية لتحسين كفاءة الطاقة</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الطاقة المتجددة">
                    <div class="suggestion-icon">🌱</div>
                    <div class="suggestion-title">الطاقة المتجددة</div>
                    <div class="suggestion-desc">حلول الطاقة النظيفة والاستثمار في المستقبل المستدام</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الأجهزة الذكية">
                    <div class="suggestion-icon">🏠</div>
                    <div class="suggestion-title">الأجهزة الذكية</div>
                    <div class="suggestion-desc">تقنيات المنزل الذكي لتحسين إدارة الطاقة</div>
                </div>
            </div>
        </div>

        <div class="messages-area" id="messagesArea"></div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-bubble">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                يحضر الإجابة...
            </div>
        </div>

        <div class="input-section">
            <div class="input-container">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="شاركني استفسارك حول الطاقة..."
                    rows="1"
                ></textarea>
                <button class="send-btn" id="sendBtn" disabled>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
            
            <div class="input-tools">
                <button class="tool-btn">
                    <span>🎤</span>
                    <span>التحدث</span>
                </button>
                <button class="tool-btn">
                    <span>📎</span>
                    <span>إرفاق ملف</span>
                </button>
                <button class="tool-btn">
                    <span>🔍</span>
                    <span>بحث متقدم</span>
                </button>
                <button class="tool-btn">
                    <span>📈</span>
                    <span>تقرير مفصل</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        const welcomeSection = document.getElementById('welcomeSection');
        const messagesArea = document.getElementById('messagesArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        const suggestionCards = document.querySelectorAll('.suggestion-card');

        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            sendBtn.disabled = this.value.trim() === '';
        });

        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        suggestionCards.forEach(card => {
            card.addEventListener('click', function() {
                const suggestion = this.getAttribute('data-suggestion');
                startChat(suggestion);
            });
        });

        function startChat(message) {
            welcomeSection.style.display = 'none';
            messagesArea.style.display = 'block';
            addMessage(message, 'user');
            showTyping();
            
            setTimeout(() => {
                hideTyping();
                const responses = {
                    'تحليل استهلاك الطاقة': 'ممتاز! سأقوم بمساعدتك في إجراء تحليل شامل لاستهلاك الطاقة:\n\n📋 **خطة التحليل:**\n• مراجعة الفواتير والبيانات التاريخية\n• تحديد الأجهزة عالية الاستهلاك\n• تحليل أنماط الاستخدام اليومية والموسمية\n• حساب التكلفة لكل وحدة ونشاط\n• تحديد فرص التحسين والتوفير\n\n📊 **النتائج المتوقعة:**\n• تقرير مفصل بالاستهلاك\n• خطة عمل للتحسين\n• توقعات التوفير المالي\n\nهل لديك فواتير كهرباء حديثة يمكنني مساعدتك في تحليلها؟',
                    'نصائح توفير الطاقة': 'سعيد لمساعدتك في تحسين كفاءة الطاقة! إليك أهم الاستراتيجيات المثبتة علمياً:\n\n💡 **الإضاءة الذكية:**\n• استبدال المصابيح بـ LED (توفير 75-80%)\n• استخدام أجهزة استشعار الحركة\n• الاستفادة من الإضاءة الطبيعية\n\n❄️ **التكييف والتدفئة:**\n• ضبط الحرارة على 24°م صيفاً، 20°م شتاءً\n• صيانة دورية للأجهزة\n• عزل حراري محسن\n\n🔌 **إدارة الأجهزة:**\n• فصل الأجهزة في وضع الاستعداد\n• استخدام مشتركات ذكية\n• اختيار أجهزة بتصنيف طاقة عالي\n\nأي من هذه المجالات تريد التركيز عليه أولاً؟',
                    'الطاقة المتجددة': 'الطاقة المتجددة استثمار استراتيجي ممتاز! دعني أوضح لك الخيارات المتاحة:\n\n☀️ **الطاقة الشمسية:**\n• عائد استثمار: 5-7 سنوات\n• توفير: 70-90% من فاتورة الكهرباء\n• عمر النظام: 25+ سنة\n• دعم حكومي متاح في معظم البلدان\n\n💨 **طاقة الرياح:**\n• مناسبة للمناطق ذات الرياح المستمرة\n• استثمار طويل المدى\n• صديقة للبيئة\n\n🔋 **أنظمة التخزين:**\n• بطاريات ليثيوم متطورة\n• تخزين الطاقة الفائضة\n• استقلالية أكبر عن الشبكة\n\n📍 **التقييم المطلوب:**\nلتقديم توصية دقيقة، أحتاج معرفة:\n• موقعك الجغرافي\n• متوسط استهلاكك الشهري\n• مساحة السطح المتاحة\n\nهل تريد حساب الجدوى الاقتصادية لمشروعك؟',
                    'الأجهزة الذكية': 'تقنيات المنزل الذكي تحدث ثورة حقيقية في إدارة الطاقة! إليك أهم الحلول:\n\n🏠 **أنظمة التحكم المركزي:**\n• منظمات الحرارة الذكية (توفير 10-15%)\n• تحكم عن بُعد وجدولة تلقائية\n• تعلم عادات الاستخدام\n\n💡 **الإضاءة الذكية:**\n• تحكم في الإضاءة والألوان\n• استشعار الحضور والغياب\n• توفير يصل إلى 60%\n\n🔌 **المقابس والمفاتيح الذكية:**\n• مراقبة استهلاك كل جهاز\n• جدولة تشغيل الأجهزة\n• إنذارات الاستهلاك العالي\n\n📱 **تطبيقات المراقبة:**\n• تتبع الاستهلاك في الوقت الفعلي\n• تقارير مفصلة وتحليلات\n• توصيات شخصية للتوفير\n\n🤖 **الذكاء الاصطناعي:**\n• تحسين تلقائي للاستهلاك\n• توقع الأعطال قبل حدوثها\n• تكيف مع أنماط الحياة\n\nما نوع الأجهزة التي تهتم بها أكثر؟'
                };
                
                const response = responses[message] || 'شكراً لثقتك! هذا سؤال ممتاز في مجال الطاقة. دعني أقدم لك معلومات شاملة ومفيدة...';
                addMessage(response, 'bot');
            }, 2200);
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            if (welcomeSection.style.display !== 'none') {
                startChat(message);
            } else {
                addMessage(message, 'user');
                showTyping();
                
                setTimeout(() => {
                    hideTyping();
                    addMessage('شكراً لسؤالك المهم! دعني أحضر لك معلومات دقيقة ومفيدة حول هذا الموضوع...', 'bot');
                }, 1800);
            }

            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendBtn.disabled = true;
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.innerHTML = `<div class="message-bubble">${text.replace(/\n/g, '<br>')}</div>`;
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }
    </script>
</body>
</html>
