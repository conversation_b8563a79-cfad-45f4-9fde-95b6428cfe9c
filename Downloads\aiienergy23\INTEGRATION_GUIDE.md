# دليل التكامل - Energy.AI Backend & Frontend Integration

## 🎯 نظرة عامة

تم تطوير نظام تكامل شامل بين الباك إند والفرونت إند لموقع Energy.AI، يتضمن:

- **Backend API** مطور بـ Node.js و Express
- **Frontend API Client** موحد للتعامل مع جميع API calls
- **نظام مصادقة متكامل** مع JWT tokens
- **تكامل مع Google Gemini AI** للدردشة الذكية
- **قاعدة بيانات SQLite** لحفظ البيانات

## 🚀 تشغيل النظام

### 1. تشغيل الباك إند

```bash
cd energy-ai-backend
npm install
node server.js
```

الخادم سيعمل على: `http://localhost:3001`

### 2. تشغيل الفرونت إند

افتح `index.html` في المتصفح أو استخدم خادم محلي:

```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx http-server

# باستخدام Live Server في VS Code
```

## 🔧 البنية التقنية

### الباك إند (Backend)

**الملفات الرئيسية:**
- `energy-ai-backend/server.js` - الخادم الرئيسي
- `energy-ai-backend/.env` - متغيرات البيئة
- `energy-ai-backend/energy-ai.db` - قاعدة البيانات

**API Endpoints:**

#### المصادقة (Authentication)
- `POST /api/auth/register` - تسجيل مستخدم جديد
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/me` - الحصول على بيانات المستخدم الحالي
- `POST /api/auth/logout` - تسجيل الخروج

#### الدردشة (Chat)
- `POST /api/chat` - إرسال رسالة للذكاء الاصطناعي
- `GET /api/chat/conversations` - الحصول على المحادثات
- `GET /api/chat/conversations/:id/messages` - رسائل محادثة معينة
- `DELETE /api/chat/conversations/:id` - حذف محادثة

#### الاتصال (Contact)
- `POST /api/contact` - إرسال رسالة اتصال

#### البيانات (Data)
- `GET /api/data` - الحصول على بيانات الطاقة
- `POST /api/data` - إضافة بيانات طاقة جديدة

#### الإدارة (Admin)
- `POST /api/admin/auth` - تسجيل دخول المدير
- `GET /api/admin/users` - قائمة المستخدمين
- `GET /api/admin/contact-messages` - رسائل الاتصال

#### أخرى
- `GET /api/health` - فحص صحة الخادم

### الفرونت إند (Frontend)

**الملفات الرئيسية:**
- `js/api-client.js` - عميل API موحد
- `js/auth.js` - نظام المصادقة
- `js/config.js` - إعدادات التطبيق
- `js/gemini-chat.js` - نظام الدردشة

**المميزات:**
- **API Client موحد** للتعامل مع جميع الطلبات
- **Error Handling متقدم** مع رسائل خطأ واضحة
- **Token Management** تلقائي
- **CORS Support** للعمل مع domains مختلفة

## 🔐 نظام المصادقة

### تسجيل مستخدم جديد
```javascript
const response = await window.apiClient.register({
    name: "اسم المستخدم",
    email: "<EMAIL>",
    password: "password123"
});
```

### تسجيل الدخول
```javascript
const response = await window.apiClient.login({
    email: "<EMAIL>",
    password: "password123"
});
```

### استخدام API مع المصادقة
```javascript
// API Client يضيف token تلقائياً
const userData = await window.apiClient.getCurrentUser();
```

## 💬 نظام الدردشة

### إرسال رسالة
```javascript
const response = await window.apiClient.sendMessage("كيف يمكنني توفير الطاقة؟");
console.log(response.response); // رد الذكاء الاصطناعي
```

### الحصول على المحادثات
```javascript
const conversations = await window.apiClient.getConversations();
```

## 🗄️ قاعدة البيانات

### الجداول المنشأة تلقائياً:

1. **users** - بيانات المستخدمين
2. **conversations** - المحادثات
3. **messages** - الرسائل
4. **contact_messages** - رسائل الاتصال
5. **energy_data** - بيانات الطاقة

## 🧪 اختبار النظام

### 1. اختبار تلقائي
افتح `test-integration.html` في المتصفح لاختبار جميع الوظائف.

### 2. اختبار يدوي
```javascript
// في console المتصفح
await window.apiClient.healthCheck(); // فحص الخادم
await window.apiClient.register({...}); // تسجيل مستخدم
await window.apiClient.sendMessage("أهلاً وسهلاً"); // إرسال رسالة
```

## ⚙️ الإعدادات

### متغيرات البيئة (.env)
```env
PORT=3001
NODE_ENV=development
JWT_SECRET=your-secret-key
GEMINI_API_KEY=your-gemini-api-key
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
```

### إعدادات CORS
```javascript
// في server.js
const corsOptions = {
    origin: ['http://localhost:3000', 'https://your-domain.com'],
    credentials: true
};
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ CORS**
   - تأكد من إضافة domain الفرونت إند في CORS settings
   - تحقق من أن الخادم يعمل على المنفذ الصحيح

2. **خطأ Database**
   - تأكد من وجود ملف `energy-ai.db`
   - تحقق من صلاحيات الكتابة في المجلد

3. **خطأ Authentication**
   - تأكد من صحة JWT_SECRET
   - تحقق من انتهاء صلاحية Token

4. **خطأ Gemini AI**
   - تأكد من صحة GEMINI_API_KEY
   - تحقق من حدود الاستخدام

## 📝 ملاحظات مهمة

1. **الأمان**: غير كلمات المرور الافتراضية في الإنتاج
2. **SSL**: استخدم HTTPS في الإنتاج
3. **Rate Limiting**: مفعل افتراضياً (100 طلب/15 دقيقة)
4. **Database Backup**: انسخ ملف `.db` احتياطياً بانتظام

## 🚀 النشر (Deployment)

### Vercel (Backend)
```bash
npm install -g vercel
vercel --prod
```

### Netlify (Frontend)
```bash
npm install -g netlify-cli
netlify deploy --prod
```

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راجع ملف `test-integration.html` للاختبارات
- تحقق من console المتصفح للأخطاء

---

**تم التطوير بواسطة:** Energy.AI Team  
**التاريخ:** 2025-01-05  
**الإصدار:** 1.0.0
