# Disable caching for development
<IfModule mod_headers.c>
    # Disable caching for HTML files
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # Disable caching for CSS files in development
    <FilesMatch "\.(css)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # Disable caching for JavaScript files in development
    <FilesMatch "\.(js)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # Add timestamp to prevent caching
    Header add X-Timestamp "%D %t"
    Header add X-Cache-Control "no-cache"
</IfModule>

# Prevent directory browsing
Options -Indexes

# Enable CORS for development
<IfModule mod_headers.c>
    Header add Access-Control-Allow-Origin "*"
    Header add Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE"
    Header add Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

# Force UTF-8 encoding
AddDefaultCharset UTF-8

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
