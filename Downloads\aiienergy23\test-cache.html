<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Cache Test - Updated Version</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            margin: 0 auto;
        }
        .timestamp {
            font-size: 24px;
            font-weight: bold;
            color: #ffff00;
            margin: 20px 0;
        }
        .status {
            background: #00ff00;
            color: #000;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Cache Test Page</h1>
        <div class="status">✅ NEW VERSION LOADED SUCCESSFULLY</div>
        <div class="timestamp" id="timestamp"></div>
        <p>إذا كنت ترى هذه الصفحة، فهذا يعني أن الخادم يعمل بشكل صحيح ويقرأ الملفات المحدثة.</p>
        <p>If you see this page, it means the server is working correctly and reading updated files.</p>
    </div>

    <script>
        // عرض الوقت الحالي لتأكيد أن الصفحة محدثة
        function updateTimestamp() {
            const now = new Date();
            document.getElementById('timestamp').textContent = 
                'Last Updated: ' + now.toLocaleString('ar-EG');
        }
        updateTimestamp();
        setInterval(updateTimestamp, 1000);
    </script>
</body>
</html>
