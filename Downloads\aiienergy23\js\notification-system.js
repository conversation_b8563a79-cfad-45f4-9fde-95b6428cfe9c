/**
 * Advanced Notification System for Energy.AI
 * نظام الإشعارات المتقدم لموقع Energy.AI
 */

class NotificationSystem {
    constructor() {
        this.notifications = new Map();
        this.queue = [];
        this.isProcessing = false;
        this.maxNotifications = 3;
        this.defaultDuration = 5000;
        this.init();
    }

    init() {
        this.createContainer();
        this.setupEventListeners();
        this.requestPermission();
        console.log('🔔 Notification System initialized');
    }

    createContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            container.innerHTML = `
                <style>
                    .notification-container {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        max-width: 400px;
                        pointer-events: none;
                    }
                    
                    .notification {
                        background: linear-gradient(135deg, #1976d2, #42a5f5);
                        color: white;
                        padding: 1rem 1.5rem;
                        margin-bottom: 10px;
                        border-radius: 8px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                        transform: translateX(100%);
                        transition: all 0.3s ease;
                        pointer-events: auto;
                        position: relative;
                        overflow: hidden;
                        backdrop-filter: blur(10px);
                    }
                    
                    .notification.show {
                        transform: translateX(0);
                    }
                    
                    .notification.success {
                        background: linear-gradient(135deg, #4caf50, #66bb6a);
                    }
                    
                    .notification.warning {
                        background: linear-gradient(135deg, #ff9800, #ffb74d);
                    }
                    
                    .notification.error {
                        background: linear-gradient(135deg, #f44336, #ef5350);
                    }
                    
                    .notification.info {
                        background: linear-gradient(135deg, #2196f3, #64b5f6);
                    }
                    
                    .notification-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 0.5rem;
                    }
                    
                    .notification-title {
                        font-weight: 600;
                        font-size: 1rem;
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                    }
                    
                    .notification-close {
                        background: none;
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 1.2rem;
                        opacity: 0.8;
                        transition: opacity 0.2s ease;
                        padding: 0;
                        width: 24px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .notification-close:hover {
                        opacity: 1;
                    }
                    
                    .notification-message {
                        font-size: 0.9rem;
                        line-height: 1.4;
                        opacity: 0.95;
                    }
                    
                    .notification-actions {
                        margin-top: 0.75rem;
                        display: flex;
                        gap: 0.5rem;
                    }
                    
                    .notification-action {
                        background: rgba(255, 255, 255, 0.2);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        color: white;
                        padding: 0.4rem 0.8rem;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 0.8rem;
                        transition: all 0.2s ease;
                    }
                    
                    .notification-action:hover {
                        background: rgba(255, 255, 255, 0.3);
                    }
                    
                    .notification-progress {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        height: 3px;
                        background: rgba(255, 255, 255, 0.3);
                        transition: width linear;
                    }
                    
                    @media (max-width: 768px) {
                        .notification-container {
                            top: 10px;
                            right: 10px;
                            left: 10px;
                            max-width: none;
                        }
                        
                        .notification {
                            margin-bottom: 8px;
                            padding: 0.8rem 1rem;
                        }
                    }
                    
                    /* RTL Support */
                    [dir="rtl"] .notification-container {
                        right: auto;
                        left: 20px;
                    }
                    
                    [dir="rtl"] .notification {
                        transform: translateX(-100%);
                    }
                    
                    [dir="rtl"] .notification.show {
                        transform: translateX(0);
                    }
                </style>
            `;
            document.body.appendChild(container);
        }
    }

    setupEventListeners() {
        // Listen for system events
        document.addEventListener('authStateChanged', (e) => {
            if (e.detail.isLoggedIn) {
                this.show('success', 'تم تسجيل الدخول بنجاح', `أهلاً وسهلاً ${e.detail.user?.name || 'بك'}`);
            }
        });

        document.addEventListener('languageChanged', (e) => {
            this.show('info', 'تم تغيير اللغة', `تم التبديل إلى ${e.detail.language === 'ar' ? 'العربية' : 'English'}`);
        });

        // Listen for network status
        window.addEventListener('online', () => {
            this.show('success', 'الاتصال متاح', 'تم استعادة الاتصال بالإنترنت');
        });

        window.addEventListener('offline', () => {
            this.show('warning', 'لا يوجد اتصال', 'تم فقدان الاتصال بالإنترنت. بعض الميزات قد لا تعمل.');
        });
    }

    async requestPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            try {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    this.show('success', 'تم تفعيل الإشعارات', 'ستتلقى إشعارات حول التحديثات المهمة');
                }
            } catch (error) {
                console.warn('Notification permission request failed:', error);
            }
        }
    }

    show(type = 'info', title, message, options = {}) {
        const notification = {
            id: Date.now() + Math.random(),
            type,
            title,
            message,
            duration: options.duration || this.defaultDuration,
            actions: options.actions || [],
            persistent: options.persistent || false,
            onClick: options.onClick,
            onClose: options.onClose
        };

        if (this.notifications.size >= this.maxNotifications) {
            this.queue.push(notification);
        } else {
            this.display(notification);
        }

        return notification.id;
    }

    display(notification) {
        const container = document.getElementById('notification-container');
        if (!container) return;

        const element = document.createElement('div');
        element.className = `notification ${notification.type}`;
        element.dataset.id = notification.id;

        const icon = this.getIcon(notification.type);
        const actions = notification.actions.map(action => 
            `<button class="notification-action" data-action="${action.id}">${action.label}</button>`
        ).join('');

        element.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">
                    ${icon}
                    ${notification.title}
                </div>
                <button class="notification-close" aria-label="إغلاق">×</button>
            </div>
            <div class="notification-message">${notification.message}</div>
            ${actions ? `<div class="notification-actions">${actions}</div>` : ''}
            ${!notification.persistent ? '<div class="notification-progress"></div>' : ''}
        `;

        // Event listeners
        element.querySelector('.notification-close').addEventListener('click', () => {
            this.hide(notification.id);
        });

        element.addEventListener('click', (e) => {
            if (e.target.classList.contains('notification-action')) {
                const actionId = e.target.dataset.action;
                const action = notification.actions.find(a => a.id === actionId);
                if (action && action.callback) {
                    action.callback();
                }
                this.hide(notification.id);
            } else if (notification.onClick) {
                notification.onClick();
            }
        });

        container.appendChild(element);
        this.notifications.set(notification.id, { element, notification });

        // Animate in
        requestAnimationFrame(() => {
            element.classList.add('show');
        });

        // Auto-hide if not persistent
        if (!notification.persistent) {
            const progressBar = element.querySelector('.notification-progress');
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.style.transitionDuration = `${notification.duration}ms`;
                
                setTimeout(() => {
                    progressBar.style.width = '0%';
                }, 100);
            }

            setTimeout(() => {
                this.hide(notification.id);
            }, notification.duration);
        }

        // Show browser notification if permission granted
        this.showBrowserNotification(notification);
    }

    hide(id) {
        const item = this.notifications.get(id);
        if (!item) return;

        const { element, notification } = item;
        
        element.classList.remove('show');
        
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            this.notifications.delete(id);
            
            if (notification.onClose) {
                notification.onClose();
            }
            
            // Process queue
            if (this.queue.length > 0) {
                const next = this.queue.shift();
                this.display(next);
            }
        }, 300);
    }

    showBrowserNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/images/icon-192x192.png',
                badge: '/images/icon-192x192.png',
                tag: `energy-ai-${notification.type}`,
                requireInteraction: notification.persistent
            });

            browserNotification.onclick = () => {
                window.focus();
                if (notification.onClick) {
                    notification.onClick();
                }
                browserNotification.close();
            };

            if (!notification.persistent) {
                setTimeout(() => {
                    browserNotification.close();
                }, notification.duration);
            }
        }
    }

    getIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    // Convenience methods
    success(title, message, options) {
        return this.show('success', title, message, options);
    }

    error(title, message, options) {
        return this.show('error', title, message, options);
    }

    warning(title, message, options) {
        return this.show('warning', title, message, options);
    }

    info(title, message, options) {
        return this.show('info', title, message, options);
    }

    clear() {
        this.notifications.forEach((item, id) => {
            this.hide(id);
        });
        this.queue = [];
    }

    destroy() {
        this.clear();
        const container = document.getElementById('notification-container');
        if (container) {
            container.remove();
        }
        console.log('🔔 Notification System destroyed');
    }
}

// Initialize notification system
document.addEventListener('DOMContentLoaded', () => {
    window.notificationSystem = new NotificationSystem();
});

// Export for use in other modules
window.NotificationSystem = NotificationSystem;
