{"version": "0.2.0", "configurations": [{"name": "Launch Live Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/live-server/live-server.js", "args": ["--port=3000", "--host=127.0.0.1", "--no-browser", "--ignore=node_modules,*.scss,*.sass,*.ts", "--wait=100", "--cors", "--no-css-inject"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Launch with Python Server", "type": "node", "request": "launch", "program": "${workspaceFolder}", "runtimeExecutable": "python", "runtimeArgs": ["-m", "http.server", "3000", "--bind", "127.0.0.1"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}